import {Component, OnInit, ViewChild} from '@angular/core';
import {InventUserDTO, InventUserSearchDTO} from '../../../interfaces/auction-import/invent-user.interface';
import {InventUserService} from '../services/invent-user.service';
import {ContactService, CustomerService, HelpersService} from '../../../global/services';
import {StatusEnum} from '../../../global/enums';
import {ModalDirective} from 'ng-uikit-pro-standard';
import {NgForm} from '@angular/forms';
import {ContactDTO, ContactSearchDTO, CustomerDTO} from "../../../../../../common/interfaces";

@Component({
  selector: 'app-invent-users',
  templateUrl: './invent-users.component.html',
  styleUrls: ['./invent-users.component.scss']
})
export class InventUsersComponent implements OnInit {

  @ViewChild('editModal', {static: false}) editModal: ModalDirective;
  @ViewChild('inventUserForm', {static: false}) inventUserForm: NgForm;

  inventUsers: InventUserDTO[] = [];
  customers: CustomerDTO[] = [];
  contacts: ContactDTO[] = [];
  customerContacts: ContactDTO[] = [];
  customerAddresses: any[] = []; // Array to hold addresses for selected customer

  totalItems: number = 0;
  currentPage: number = 1;
  itemsPerPage: number = 20;
  loading: boolean = false;

  // Pagination
  pageSizes: number[] = [10, 20, 50, 100];

  // Search filters
  searchTerm: string = '';

  // Edit modal
  editingUser: InventUserDTO = null;
  isEditMode: boolean = false;
  modalTitle: string = '';

  // Status mapping
  statusName: {} = {};

  constructor(
    private inventUserService: InventUserService,
    private helpersService: HelpersService,
    private contactService: ContactService
  ) {
    this.statusName = this.helpersService.getValuesByIndex(StatusEnum);
  }

  async ngOnInit() {
    await this.loadInventUsers();

    const dto = {
      component: 'invent-user-admin',
      filters: {}
    } as ContactSearchDTO;

    this.contactService.search(dto).then(res => {
      this.contacts = res.results.filter(x => x.primaryAddress);

      console.log("** Contacts: ", this.contacts);

      // extract unique customers from contacts using Map
      const customerMap = new Map();
      this.contacts.forEach(contact => {
        if (!customerMap.has(contact.customer.id)) {
          customerMap.set(contact.customer.id, {
            id: String(contact.customer.id), // Ensure string type
            customerName: contact.customer.customerName,
            email: contact.customer.email,
            phone1: contact.customer.phone1
          } as CustomerDTO);
        }
      });

      this.customers = Array.from(customerMap.values());

    }).catch((err) => {
      console.error("Failed to fetch contacts");
    });
  }

  /**
   * Load invent users with current filters and pagination
   */
  async loadInventUsers() {
    this.loading = true;

    try {
      const searchDTO: InventUserSearchDTO = {
        offset: (this.currentPage - 1) * this.itemsPerPage,
        limit: this.itemsPerPage,
        component: 'invent-users',
        filters: {
          searchTerm: this.searchTerm || undefined
        }
      };

      const result = await this.inventUserService.search(searchDTO);
      this.inventUsers = result.results || [];
      this.totalItems = result.totalItems || 0;
    } catch (error) {
      console.error('Error loading invent users:', error);
      this.inventUsers = [];
      this.totalItems = 0;
    } finally {
      this.loading = false;
    }
  }

  /**
   * Handle search input changes with debounce
   */
  onSearchChange() {
    // Reset to first page when searching
    this.currentPage = 1;
    this.loadInventUsers();
  }

  /**
   * Handle page changes
   */
  onPageChange(page: number) {
    this.currentPage = page;
    this.loadInventUsers();
  }

  /**
   * Handle page size changes
   */
  handlePageSizeChange(event: any) {
    this.itemsPerPage = event.target.value;
    this.currentPage = 1;
    this.loadInventUsers();
  }

  /**
   * Open modal to add new invent user
   */
  addInventUser() {
    this.editingUser = {
      companyName: '',
      auctionId: '',
      contactId: '',
      contactName: '',
      customerId: '',
      customerName: '',
      addressId: '',
      logoSwapPixelHeight: 0,
      customerRef: '',
      auctionMarkup: 0,
      useImageRecognition: false,
      statusId: StatusEnum.Active
    };
    this.isEditMode = false;
    this.modalTitle = 'Add Invent User';

    // Reset dependent dropdowns
    this.customerContacts = [];
    this.customerAddresses = [];

    this.editModal.show();
  }

  /**
   * Open modal to edit existing invent user
   */
  editInventUser(user: InventUserDTO) {
    this.editingUser = { ...user }; // Create a copy

    // Ensure IDs are strings for mdb-select compatibility
    this.editingUser.customerId = String(this.editingUser.customerId || '');
    this.editingUser.contactId = String(this.editingUser.contactId || '');
    this.editingUser.addressId = String(this.editingUser.addressId || '');

    this.isEditMode = true;
    this.modalTitle = 'Edit Invent User';

    // Load contacts and addresses for the selected customer
    if (this.editingUser.customerId) {
      this.loadCustomerContactsAndAddresses(this.editingUser.customerId);
    }

    this.editModal.show();
  }

  /**
   * Save invent user (create or update)
   */
  async saveInventUser() {
    if (!this.editingUser) return;

    try {
      // remove dto-only properties and create cleanUser
      const { contactName, customerName, statusId, added, updated, ...cleanUser } = this.editingUser;

      if (this.isEditMode) {
        // Find the original user for comparison
        const originalUser = this.inventUsers.find(u => u.id === this.editingUser.id);
        await this.inventUserService.patch(this.editingUser.id, cleanUser, originalUser);
      } else {
        await this.inventUserService.create(cleanUser);
      }

      this.editModal.hide();
      await this.loadInventUsers();
    } catch (error) {
      console.error('Error saving invent user:', error);
      // TODO: Show error message to user
    }
  }

  /**
   * Delete invent user with confirmation
   */
  async deleteInventUser(user: InventUserDTO) {
    if (!confirm(`Are you sure you want to delete invent user "${user.companyName}"?`)) {
      return;
    }

    try {
      await this.inventUserService.delete(user.id);
      await this.loadInventUsers();
    } catch (error) {
      console.error('Error deleting invent user:', error);
      // TODO: Show error message to user
    }
  }

  /**
   * Cancel edit modal
   */
  cancelEdit() {
    this.editModal.hide();
    this.editingUser = null;
  }

  /**
   * Get status display name
   */
  getStatusName(statusId: number): string {
    return this.statusName[statusId] || 'Unknown';
  }

  /**
   * Format date for display
   */
  formatDate(date: Date | string): string {
    if (!date) return 'Never';
    return new Date(date).toLocaleDateString('en-GB');
  }

  /**
   * Handle customer selection change
   */
  onCustomerChange($event: any) {
    this.loadCustomerContactsAndAddresses($event);

    // Reset contact and address selections
    this.editingUser.contactId = '';
    this.editingUser.addressId = '';
  }

  /**
   * Load contacts and addresses for selected customer
   */
  private loadCustomerContactsAndAddresses(customerId: any) {
    // Convert to string for consistent comparison
    const customerIdStr = String(customerId);

    // Filter contacts for selected customer
    this.customerContacts = this.contacts.filter(x => String(x.customer.id) === customerIdStr);

    // Extract unique addresses from contacts for the selected customer
    const addressMap = new Map();
    this.customerContacts.forEach(contact => {
      if (contact.primaryAddress && !addressMap.has(contact.primaryAddress.id)) {
        addressMap.set(contact.primaryAddress.id, {
          id: String(contact.primaryAddress.id), // Ensure string type
          addressName: contact.primaryAddress.addressName,
          addressLine1: contact.primaryAddress.addressLine1,
          addressLine2: contact.primaryAddress.addressLine2,
          addressLine3: contact.primaryAddress.addressLine3,
          addressCounty: contact.primaryAddress.addressCounty,
          addressPostcode: contact.primaryAddress.addressPostcode,
          // Add a display name for the dropdown
          displayName: this.formatAddressDisplay(contact.primaryAddress)
        });
      }
    });

    this.customerAddresses = Array.from(addressMap.values());

    // Auto-select first contact and address if available (use setTimeout to ensure dropdown is ready)
    setTimeout(() => {
      if (this.customerContacts.length > 0 && !this.editingUser.contactId) {
        this.editingUser.contactId = String(this.customerContacts[0].id);
      }

      if (this.customerAddresses.length > 0 && !this.editingUser.addressId) {
        this.editingUser.addressId = String(this.customerAddresses[0].id);
      }
    }, 100);
  }

  /**
   * Format address for display in dropdown
   */
  private formatAddressDisplay(address: any): string {
    const parts = [
      address.addressName,
      address.addressLine1,
      address.addressLine2,
      address.addressLine3,
      address.addressCounty,
      address.addressPostcode
    ].filter(part => part && part.trim() !== '');

    return parts.join(', ');
  }

  /**
   * Handle contact selection change
   */
  onContactChange($event: any) {
    // Optional: You could implement logic here if needed
    // For example, if you want to auto-select an address based on contact
  }

  /**
   * Handle address selection change
   */
  onAddressChange($event: any) {
  }
}
