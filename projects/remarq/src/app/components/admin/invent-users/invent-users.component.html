<h1 class="page-header">Invent Users</h1>

<!-- Search and Actions Bar -->
<div class="row mb-3">
  <div class="col-md-6">
    <div class="input-group">
      <input
        type="text"
        class="form-control"
        placeholder="Search by company name, contact, or customer..."
        [(ngModel)]="searchTerm"
        (ngModelChange)="onSearchChange()"
      >
      <div class="input-group-append">
        <span class="input-group-text">
          <i class="fas fa-search"></i>
        </span>
      </div>
    </div>
  </div>
  <div class="col-md-6 text-right">
    <button
      type="button"
      class="btn btn-primary"
      (click)="addInventUser()"
    >
      <i class="fas fa-plus"></i> Add Invent User
    </button>
  </div>
</div>

<!-- Loading Indicator -->
<div *ngIf="loading" class="text-center py-4">
  <div class="spinner-border" role="status">
    <span class="sr-only">Loading...</span>
  </div>
</div>

<!-- Results Table -->
<div class="widget mt-2" *ngIf="!loading">
  <table class="table table-striped table-condensed vertical-centre-cells" mdbTable>
    <thead>
    <tr>
      <th>Company Name</th>
      <th>Auction ID</th>
      <th>Contact</th>
      <th>Customer</th>
      <th>Customer Ref</th>
      <th>Logo Swap</th>
      <th>Auction Markup</th>
      <th>Image Recognition</th>
      <th>Last Import</th>
      <th>Status</th>
      <th>Actions</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let user of inventUsers | paginate: { itemsPerPage: itemsPerPage, currentPage: currentPage, totalItems: totalItems }">
      <td>
        <div class="table-line-1">{{ user.companyName }}</div>
      </td>
      <td>
        <div class="table-line-1">{{ user.auctionId }}</div>
      </td>
      <td>
        <div class="table-line-1">{{ user.contactName }}</div>
      </td>
      <td>
        <div class="table-line-1">{{ user.customerName }}</div>
      </td>
      <td>
        <div class="table-line-1">{{ user.customerRef }}</div>
      </td>
      <td>
        <div class="table-line-1">
          <span *ngIf="user.logoSwapPixelHeight > 0">{{ user.logoSwapPixelHeight }}px</span>
          <span *ngIf="user.logoSwapPixelHeight <= 0" class="text-muted">Disabled</span>
        </div>
      </td>
      <td>
        <div class="table-line-1">
          <span *ngIf="user?.auctionMarkup > 0">{{ user.auctionMarkup }}</span>
          <span *ngIf="user?.auctionMarkup <= 0" class="text-muted">Disabled</span>
        </div>
      </td>
      <td>
        <div class="table-line-1">
          <span *ngIf="user.useImageRecognition" class="text-success">
            <i class="fas fa-check"></i> Enabled
          </span>
          <span *ngIf="!user.useImageRecognition" class="text-muted">
            <i class="fas fa-times"></i> Disabled
          </span>
        </div>
      </td>
      <td>
        <div class="table-line-1">{{ formatDate(user.added) }}</div>
      </td>
      <td>
        <div class="btn btn-xxs status status-{{ user.statusId }}">
          {{ getStatusName(user.statusId) }}
        </div>
      </td>
      <td>
        <button
          type="button"
          class="btn btn-sm btn-outline-primary mr-1"
          (click)="editInventUser(user)"
          title="Edit"
        >
          <i class="fas fa-edit"></i>
        </button>
        <button
          type="button"
          class="btn btn-sm btn-outline-danger"
          (click)="deleteInventUser(user)"
          title="Delete"
        >
          <i class="fas fa-trash"></i>
        </button>
      </td>
    </tr>
    <tr *ngIf="inventUsers.length === 0">
      <td colspan="11" class="text-center text-muted py-4">
        <i class="fas fa-inbox fa-2x mb-2"></i>
        <div>No invent users found</div>
      </td>
    </tr>
    </tbody>
  </table>
</div>

<!-- Pagination -->
<div class="row mt-3" *ngIf="!loading && totalItems > itemsPerPage">
  <div class="col-6 pagination-page-size">
    Items per Page:
    <select (change)="handlePageSizeChange($event)" class="form-control w-auto inline-block">
      <option *ngFor="let size of pageSizes" [ngValue]="size">
        {{ size }}
      </option>
    </select>
  </div>
  <div class="col-6 text-right">
    <pagination-controls
      responsive="true"
      class="paginator"
      previousLabel="Prev"
      nextLabel="Next"
      (pageChange)="onPageChange($event)">
    </pagination-controls>
  </div>
</div>

<!-- Edit Modal -->
<div
  mdbModal
  #editModal="mdbModal"
  class="modal fade"
  tabindex="-1"
  role="dialog"
  [config]="{backdrop: 'static', keyboard: false}"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ modalTitle }}</h5>
        <button
          type="button"
          class="close"
          (click)="cancelEdit()"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" *ngIf="editingUser">
        <form #inventUserForm="ngForm">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="companyName">Company Name *</label>
                <input
                  type="text"
                  id="companyName"
                  class="form-control"
                  [(ngModel)]="editingUser.companyName"
                  name="companyName"
                  required
                >
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="auctionId">Auction ID *</label>
                <input
                  type="text"
                  id="auctionId"
                  class="form-control"
                  [(ngModel)]="editingUser.auctionId"
                  name="auctionId"
                  required
                >
              </div>
            </div>
          </div>

          <!-- Customer, Contact, Address row - reordered as requested -->
          <div class="row">
            <div class="col-md-4">
              <div class="form-group select select-sm" style="margin-bottom: 45px">
                <label for="customerName">Customer *</label>
                <mdb-select-2
                  [outline]="true"
                  required
                  name="customerId"
                  [(ngModel)]="editingUser.customerId"
                  (ngModelChange)="onCustomerChange($event)"
                  placeholder="Select Customer">
                  <mdb-select-option *ngFor="let customer of customers"
                                     [value]="customer.id.toString()">{{ customer.customerName }}</mdb-select-option>
                </mdb-select-2>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group select select-sm" style="margin-bottom: 45px">
                <label for="contactName">Contact *</label>
                <mdb-select-2
                  [outline]="true"
                  name="contactId"
                  required
                  [(ngModel)]="editingUser.contactId"
                  (ngModelChange)="onContactChange($event)"
                  placeholder="Select Contact">
                  <mdb-select-option *ngFor="let contact of customerContacts"
                                     [value]="contact.id.toString()">{{ contact.contactName }}</mdb-select-option>
                </mdb-select-2>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group select select-sm" style="margin-bottom: 45px">
                <label for="addressId">Address *</label>
                <mdb-select-2
                  [outline]="true"
                  name="addressId"
                  required
                  [(ngModel)]="editingUser.addressId"
                  (ngModelChange)="onAddressChange($event)"
                  placeholder="Select Address">
                  <mdb-select-option *ngFor="let address of customerAddresses"
                                     [value]="address.id">{{ address.displayName }}</mdb-select-option>
                </mdb-select-2>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="customerRef">Customer Reference</label>
                <input
                  type="text"
                  id="customerRef"
                  class="form-control"
                  [(ngModel)]="editingUser.customerRef"
                  name="customerRef"
                  placeholder="Used for imageKit prefix and appraisal reference"
                >
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="logoSwapPixelHeight">Logo Swap Height (pixels)</label>
                <input
                  type="number"
                  id="logoSwapPixelHeight"
                  class="form-control"
                  [(ngModel)]="editingUser.logoSwapPixelHeight"
                  name="logoSwapPixelHeight"
                  min="0"
                  placeholder="0 = disabled, >0 = enabled with height"
                >
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="auctionMarkup">Auction Markup</label>
                <input
                  type="number"
                  id="auctionMarkup"
                  class="form-control"
                  [(ngModel)]="editingUser.auctionMarkup"
                  name="auctionMarkup"
                  min="0"
                  placeholder="0 = disabled, >0 = enabled"
                >
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="statusId">Status</label>
                <select
                  id="statusId"
                  class="form-control"
                  [(ngModel)]="editingUser.statusId"
                  name="statusId"
                >
                  <option [value]="1">Active</option>
                  <option [value]="2">Inactive</option>
                  <option [value]="3">Deleted</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <div class="form-check">
                  <input
                    type="checkbox"
                    id="useImageRecognition"
                    class="form-check-input"
                    [(ngModel)]="editingUser.useImageRecognition"
                    name="useImageRecognition"
                  >
                  <label class="form-check-label" for="useImageRecognition">
                    Use Image Recognition
                  </label>
                  <small class="form-text text-muted">
                    Enable image recognition to automatically crop images and paste logos
                  </small>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="cancelEdit()"
        >
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="saveInventUser()"
          [disabled]="!inventUserForm?.valid"
        >
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </div>
    </div>
  </div>
</div>
