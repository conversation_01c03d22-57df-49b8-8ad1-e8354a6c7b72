import {AddressDTO, BaseDTO, ContactDTO, CustomerDTO, BaseSearchDTO} from "../../global/interfaces";

export interface InventUserDTO extends BaseDTO {
  companyName: string;
  auctionId: string;
  lastImportedDate?: Date;

  contactId: string;
  contactName: string;
  contact?: ContactDTO;

  customerId: string;
  customerName: string;
  customer?: CustomerDTO;

  addressId: string;
  address?: AddressDTO;

  // Logo swap functionality - if > 0 then logo swap is enabled and the value is the height in pixels
  logoSwapPixelHeight: number;

  // Used for imageKit prefix and appraisal reference
  customerRef: string;

  auctionMarkup?: number // unit value to add to the imported lot (i.e. GBP, EUR, etc.)

  useImageRecognition: boolean // if true then use image recognition to crop image and paste logo
}

export interface InventUserSearchDTO extends BaseSearchDTO {
  filters?: {
    searchTerm?: string;
    companyName?: string;
    auctionId?: string;
    contactName?: string;
    customerName?: string;
  };
}
